/**
 * ScenePanel组件测试
 */
import React from 'react';
import { render, screen, fireEvent } from '../../../__tests__/utils/test-utils';
import ScenePanel from '../ScenePanel';
import { jest } from '@jest/globals';

// 模拟引擎服务
jest.mock('../../../services/EngineService', () => ({
  __esModule: true,
  default: {
    initialize: jest.fn().mockResolvedValue(undefined),
    on: jest.fn(),
    off: jest.fn(),
    getActiveScene: jest.fn(),
    getActiveCamera: jest.fn()
  },
  EngineEventType: {
    OBJECT_SELECTED: 'object-selected',
    OBJECT_DESELECTED: 'object-deselected'
  },
  TransformMode: {
    TRANSLATE: 'translate',
    ROTATE: 'rotate',
    SCALE: 'scale'
  },
  TransformSpace: {
    LOCAL: 'local',
    WORLD: 'world'
  }
}));

// 模拟场景服务
jest.mock('../../../services/SceneService', () => ({
  __esModule: true,
  default: {
    loadScene: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
    currentScene: null,
    expandedNodes: {}
  },
  SceneEventType: {
    SCENE_LOADED: 'scene-loaded',
    SCENE_UNLOADED: 'scene-unloaded'
  }
}));

describe('ScenePanel组件', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // 模拟canvas元素的getBoundingClientRect方法
    HTMLCanvasElement.prototype.getBoundingClientRect = jest.fn(() => ({
      width: 800,
      height: 600,
      top: 0,
      left: 0,
      right: 800,
      bottom: 600,
      x: 0,
      y: 0,
      toJSON: jest.fn()
    } as DOMRect));
  });

  it('应该正确渲染ScenePanel组件', () => {
    render(<ScenePanel />);
    
    // 验证canvas元素已渲染
    const canvas = screen.getByRole('img');
    expect(canvas).toBeInTheDocument();
    expect(canvas.tagName.toLowerCase()).toBe('canvas');
  });

  it('应该在鼠标按下时处理选择事件', () => {
    render(<ScenePanel />);
    
    const canvas = screen.getByRole('img');
    fireEvent.mouseDown(canvas, { clientX: 400, clientY: 300 });
    
    // 注意：由于我们模拟了引擎服务，实际上不会执行真正的射线投射逻辑
    // 这里只是验证事件处理器被调用
    expect(canvas).toBeInTheDocument();
  });

  it('应该显示加载状态', () => {
    // 修改模拟以返回未初始化状态
    const originalUseState = React.useState;
    const mockUseState = jest.spyOn(React, 'useState');

    // 模拟useState返回未初始化状态
    mockUseState.mockImplementationOnce(() => [false, jest.fn()]);
    mockUseState.mockImplementationOnce(() => [false, jest.fn()]);

    // 其他useState调用使用原始实现
    mockUseState.mockImplementation(originalUseState as any);

    render(<ScenePanel />);

    // 验证加载状态显示
    expect(screen.getByText('editor.loadingEngine')).toBeInTheDocument();

    // 恢复原始实现
    mockUseState.mockRestore();
  });
});
