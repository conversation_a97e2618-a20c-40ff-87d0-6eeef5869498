/**
 * 用户行为分析器
 * 用于记录和分析用户行为
 */
import { EventEmitter } from 'events';

/**
 * 用户行为类型
 */
export enum UserActionType {
  MOUSE_MOVE = 'mouse_move',
  MOUSE_CLICK = 'mouse_click',
  KEY_PRESS = 'key_press',
  SCROLL = 'scroll',
  COMPONENT_INTERACTION = 'component_interaction',
  MENU_SELECTION = 'menu_selection',
  TOOL_USAGE = 'tool_usage',
  PANEL_SWITCH = 'panel_switch',
  DIALOG_INTERACTION = 'dialog_interaction',
  SCENE_INTERACTION = 'scene_interaction',
  OBJECT_SELECTION = 'object_selection',
  PROPERTY_CHANGE = 'property_change',
  NAVIGATION = 'navigation',
  SEARCH = 'search',
  UNDO_REDO = 'undo_redo',
  SAVE_LOAD = 'save_load',
  COLLABORATION = 'collaboration',
  ERROR = 'error'
}

/**
 * 用户行为接口
 */
export interface UserAction {
  type: UserActionType;
  timestamp: number;
  data: any;
}

/**
 * 用户行为热点
 */
export interface HeatmapPoint {
  x: number;
  y: number;
  value: number;
}

/**
 * 用户行为分析器配置
 */
export interface UserBehaviorAnalyzerConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否记录鼠标移动 */
  recordMouseMove?: boolean;
  /** 鼠标移动采样间隔（毫秒） */
  mouseMoveInterval?: number;
  /** 是否记录键盘输入 */
  recordKeyPress?: boolean;
  /** 是否记录滚动事件 */
  recordScroll?: boolean;
  /** 历史记录大小限制 */
  historyLimit?: number;
  /** 是否启用热图分析 */
  enableHeatmap?: boolean;
  /** 热图分辨率 */
  heatmapResolution?: { width: number; height: number };
  /** 是否启用路径分析 */
  enablePathAnalysis?: boolean;
  /** 是否启用时间分析 */
  enableTimeAnalysis?: boolean;
  /** 是否启用组件使用分析 */
  enableComponentAnalysis?: boolean;
  /** 是否启用错误分析 */
  enableErrorAnalysis?: boolean;
}

/**
 * 用户行为分析器类
 */
export class UserBehaviorAnalyzer extends EventEmitter {
  private config: Required<UserBehaviorAnalyzerConfig>;
  private actionHistory: UserAction[] = [];
  private heatmapData: HeatmapPoint[] = [];
  private componentUsage: Record<string, number> = {};
  private errorCount: Record<string, number> = {};
  private pathData: { from: string; to: string; count: number }[] = [];
  private timeSpent: Record<string, number> = {};
  private lastMouseMoveTime: number = 0;
  private isRecording: boolean = false;
  private mouseMoveTimer: number | null = null;
  private sessionStartTime: number = 0;
  private currentView: string = '';
  private viewStartTime: number = 0;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: UserBehaviorAnalyzerConfig = {}) {
    super();
    
    // 默认配置
    this.config = {
      enabled: config.enabled ?? true,
      recordMouseMove: config.recordMouseMove ?? true,
      mouseMoveInterval: config.mouseMoveInterval ?? 500,
      recordKeyPress: config.recordKeyPress ?? true,
      recordScroll: config.recordScroll ?? true,
      historyLimit: config.historyLimit ?? 1000,
      enableHeatmap: config.enableHeatmap ?? true,
      heatmapResolution: config.heatmapResolution ?? { width: 100, height: 100 },
      enablePathAnalysis: config.enablePathAnalysis ?? true,
      enableTimeAnalysis: config.enableTimeAnalysis ?? true,
      enableComponentAnalysis: config.enableComponentAnalysis ?? true,
      enableErrorAnalysis: config.enableErrorAnalysis ?? true
    };
  }

  /**
   * 开始记录
   */
  public startRecording(): void {
    if (this.isRecording || !this.config.enabled) return;
    
    this.isRecording = true;
    this.sessionStartTime = Date.now();
    this.setupEventListeners();
    
    this.emit('recordingStarted');
  }

  /**
   * 停止记录
   */
  public stopRecording(): void {
    if (!this.isRecording) return;
    
    this.isRecording = false;
    this.removeEventListeners();
    
    if (this.mouseMoveTimer !== null) {
      window.clearInterval(this.mouseMoveTimer);
      this.mouseMoveTimer = null;
    }
    
    this.emit('recordingStopped');
  }

  /**
   * 记录用户行为
   * @param type 行为类型
   * @param data 行为数据
   */
  public recordAction(type: UserActionType, data: any): void {
    if (!this.isRecording || !this.config.enabled) return;
    
    const action: UserAction = {
      type,
      timestamp: Date.now(),
      data
    };
    
    this.actionHistory.push(action);
    
    // 限制历史大小
    if (this.actionHistory.length > this.config.historyLimit) {
      this.actionHistory.shift();
    }
    
    // 更新分析数据
    this.updateAnalytics(action);
    
    this.emit('actionRecorded', action);
  }

  /**
   * 获取行为历史
   * @returns 行为历史
   */
  public getActionHistory(): UserAction[] {
    return [...this.actionHistory];
  }

  /**
   * 获取热图数据
   * @returns 热图数据
   */
  public getHeatmapData(): HeatmapPoint[] {
    return [...this.heatmapData];
  }

  /**
   * 获取组件使用统计
   * @returns 组件使用统计
   */
  public getComponentUsage(): Record<string, number> {
    return { ...this.componentUsage };
  }

  /**
   * 获取错误统计
   * @returns 错误统计
   */
  public getErrorCount(): Record<string, number> {
    return { ...this.errorCount };
  }

  /**
   * 获取路径数据
   * @returns 路径数据
   */
  public getPathData(): { from: string; to: string; count: number }[] {
    return [...this.pathData];
  }

  /**
   * 获取时间统计
   * @returns 时间统计
   */
  public getTimeSpent(): Record<string, number> {
    return { ...this.timeSpent };
  }

  /**
   * 清除所有数据
   */
  public clearData(): void {
    this.actionHistory = [];
    this.heatmapData = [];
    this.componentUsage = {};
    this.errorCount = {};
    this.pathData = [];
    this.timeSpent = {};
    
    this.emit('dataCleared');
  }

  /**
   * 设置当前视图
   * @param viewName 视图名称
   */
  public setCurrentView(viewName: string): void {
    if (this.currentView === viewName) return;
    
    const now = Date.now();
    
    // 更新上一个视图的时间
    if (this.currentView && this.viewStartTime > 0) {
      const timeSpent = now - this.viewStartTime;
      this.timeSpent[this.currentView] = (this.timeSpent[this.currentView] || 0) + timeSpent;

      // 记录路径
      if (this.config.enablePathAnalysis) {
        const existingPath = this.pathData.find(p => p.from === this.currentView && p.to === viewName);

        if (existingPath) {
          existingPath.count++;
        } else {
          this.pathData.push({
            from: this.currentView,
            to: viewName,
            count: 1
          });
        }
      }
    }
    
    // 设置新视图
    this.currentView = viewName;
    this.viewStartTime = now;
    
    this.emit('viewChanged', { from: this.currentView, to: viewName });
  }

  // 私有方法
  private setupEventListeners(): void {
    // 实现事件监听器设置
  }

  private removeEventListeners(): void {
    // 实现事件监听器移除
  }

  private updateAnalytics(_action: UserAction): void {
    // 实现分析数据更新
  }
}

// 创建单例实例
export const userBehaviorAnalyzer = new UserBehaviorAnalyzer();
